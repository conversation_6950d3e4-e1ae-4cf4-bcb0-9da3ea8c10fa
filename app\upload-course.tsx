import { geminiService } from "@/lib/gemini";
import { supabase } from "@/lib/supabase";
import { useUser } from "@clerk/clerk-expo";
import { FontAwesome5, Ionicons, MaterialIcons } from "@expo/vector-icons";
import * as DocumentPicker from "expo-document-picker";
import * as FileSystem from "expo-file-system";
import { router } from "expo-router";
import React, { useState } from "react";
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

export default function UploadCourseScreen() {
  const { user } = useUser();
  const [selectedFile, setSelectedFile] =
    useState<DocumentPicker.DocumentPickerResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStep, setProcessingStep] = useState("");

  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          "application/pdf",
          "text/plain",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ],
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedFile(result);
      }
    } catch (error) {
      console.error("Error picking document:", error);
      Alert.alert("Error", "Failed to pick document");
    }
  };

  const readFileContent = async (uri: string): Promise<string> => {
    try {
      const content = await FileSystem.readAsStringAsync(uri);
      return content;
    } catch (error) {
      console.error("Error reading file:", error);
      throw new Error("Failed to read file content");
    }
  };

  const processCourse = async () => {
    if (
      !selectedFile ||
      !selectedFile.assets ||
      selectedFile.assets.length === 0
    ) {
      Alert.alert("Error", "Please select a file first");
      return;
    }

    if (!user) {
      Alert.alert("Error", "Please sign in to upload courses");
      return;
    }

    setIsProcessing(true);

    try {
      const file = selectedFile.assets[0];

      setProcessingStep("Reading file content...");
      const fileContent = await readFileContent(file.uri);

      setProcessingStep("Extracting text with AI...");
      const extractedText = await geminiService.extractTextFromDocument(
        fileContent,
        file.mimeType || "text/plain"
      );

      setProcessingStep("Analyzing course content...");
      const courseAnalysis = await geminiService.analyzeCourse(extractedText);

      setProcessingStep("Saving course to database...");

      // Get user from Supabase
      const { data: supabaseUser } = await supabase
        .from("users")
        .select("id")
        .eq("clerk_user_id", user.id)
        .single();

      if (!supabaseUser) {
        throw new Error("User not found in database");
      }

      // Save course to database
      const { data: course, error: courseError } = await supabase
        .from("courses")
        .insert({
          user_id: supabaseUser.id,
          title: courseAnalysis.title,
          description: courseAnalysis.description,
          syllabus_content: extractedText,
          difficulty_level: courseAnalysis.difficulty,
          estimated_duration_weeks: courseAnalysis.estimatedWeeks,
        })
        .select()
        .single();

      if (courseError) {
        throw courseError;
      }

      setProcessingStep("Generating study plan...");
      const studyPlan = await geminiService.generateStudyPlan(courseAnalysis);

      // Save study plan
      const { error: studyPlanError } = await supabase
        .from("study_plans")
        .insert({
          course_id: course.id,
          user_id: supabaseUser.id,
          title: studyPlan.title,
          description: studyPlan.description,
          milestones: studyPlan.milestones,
          start_date: new Date().toISOString().split("T")[0],
          target_completion_date: new Date(
            Date.now() + studyPlan.totalWeeks * 7 * 24 * 60 * 60 * 1000
          )
            .toISOString()
            .split("T")[0],
          status: "active",
        });

      if (studyPlanError) {
        console.error("Error saving study plan:", studyPlanError);
      }

      setProcessingStep("Generating flashcards...");
      const flashcardSets = await geminiService.generateFlashcards(
        courseAnalysis
      );

      // Save flashcards
      const flashcardsToInsert = flashcardSets.flatMap((set) =>
        set.cards.map((card) => ({
          course_id: course.id,
          user_id: supabaseUser.id,
          front: card.front,
          back: card.back,
          category: set.category,
          difficulty: card.difficulty,
          next_review_date: new Date().toISOString(),
          review_count: 0,
          ease_factor: 2.5,
          interval_days: 1,
        }))
      );

      if (flashcardsToInsert.length > 0) {
        const { error: flashcardsError } = await supabase
          .from("flashcards")
          .insert(flashcardsToInsert);

        if (flashcardsError) {
          console.error("Error saving flashcards:", flashcardsError);
        }
      }

      Alert.alert(
        "Success!",
        `Course "${courseAnalysis.title}" has been uploaded and processed successfully!`,
        [
          {
            text: "View Course",
            onPress: () => router.replace(`/course/${course.id}`),
          },
          {
            text: "Go to Dashboard",
            onPress: () => router.replace("/(tabs)"),
          },
        ]
      );
    } catch (error) {
      console.error("Error processing course:", error);
      Alert.alert(
        "Error",
        error instanceof Error ? error.message : "Failed to process course"
      );
    } finally {
      setIsProcessing(false);
      setProcessingStep("");
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Upload Course</Text>
      </View>

      <View style={styles.content}>
        {/* Hero Section */}
        <View style={styles.heroSection}>
          <View style={styles.titleContainer}>
            <MaterialIcons
              name="school"
              size={28}
              color="#007AFF"
              style={styles.titleIcon}
            />
            <Text style={styles.title}>Upload Your Course</Text>
          </View>
          <Text style={styles.subtitle}>
            Transform Learning Materials into Smart Study Plans
          </Text>
          <Text style={styles.description}>
            Upload any course syllabus, textbook, or learning material and our
            AI will create a personalized study plan with flashcards,
            milestones, and spaced repetition schedules.
          </Text>
        </View>

        {/* Upload Section */}
        <View style={styles.uploadSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Get Started</Text>
            <Text style={styles.sectionSubtitle}>
              Upload your document to begin
            </Text>
          </View>

          <TouchableOpacity
            style={[
              styles.uploadButton,
              selectedFile && styles.uploadButtonSelected,
            ]}
            onPress={pickDocument}
            disabled={isProcessing}
          >
            {selectedFile ? (
              <Ionicons
                name="checkmark-circle"
                size={20}
                color="#fff"
                style={styles.uploadButtonIcon}
              />
            ) : (
              <MaterialIcons
                name="folder-open"
                size={20}
                color="#fff"
                style={styles.uploadButtonIcon}
              />
            )}
            <Text style={styles.uploadButtonText}>
              {selectedFile
                ? "File Selected - Tap to Change"
                : "Select Document"}
            </Text>
          </TouchableOpacity>

          {selectedFile &&
            selectedFile.assets &&
            selectedFile.assets.length > 0 && (
              <View style={styles.fileInfo}>
                <View style={styles.fileDetails}>
                  <View style={styles.fileNameContainer}>
                    <MaterialIcons
                      name="description"
                      size={16}
                      color="#007AFF"
                    />
                    <Text style={styles.fileName}>
                      {selectedFile.assets[0].name}
                    </Text>
                  </View>
                  <Text style={styles.fileSize}>
                    Size:{" "}
                    {Math.round((selectedFile.assets[0].size || 0) / 1024)} KB
                  </Text>
                  <Text style={styles.fileType}>
                    Type:{" "}
                    {selectedFile.assets[0].mimeType
                      ?.split("/")[1]
                      ?.toUpperCase() || "Unknown"}
                  </Text>
                </View>
              </View>
            )}

          {selectedFile && (
            <TouchableOpacity
              style={[
                styles.processButton,
                isProcessing && styles.processButtonDisabled,
              ]}
              onPress={processCourse}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <View style={styles.processingContainer}>
                  <ActivityIndicator color="#fff" size="small" />
                  <Text style={styles.processButtonText}>{processingStep}</Text>
                </View>
              ) : (
                <View style={styles.processButtonContent}>
                  <Ionicons
                    name="rocket"
                    size={18}
                    color="#fff"
                    style={styles.processButtonIcon}
                  />
                  <Text style={styles.processButtonText}>
                    Start AI Processing
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          )}
        </View>

        {/* Supported File Types Section */}
        <View style={styles.supportedFormats}>
          <View style={styles.supportedTitleContainer}>
            <Text style={styles.supportedTitle}>Supported File Types</Text>
          </View>
          <View style={styles.formatsList}>
            <View style={styles.formatItem}>
              <MaterialIcons
                name="picture-as-pdf"
                size={16}
                color="#d32f2f"
                style={styles.formatIcon}
              />
              <Text style={styles.formatText}>PDF Documents</Text>
            </View>
            <View style={styles.formatItem}>
              <MaterialIcons
                name="description"
                size={16}
                color="#1976d2"
                style={styles.formatIcon}
              />
              <Text style={styles.formatText}>Word Documents (DOC, DOCX)</Text>
            </View>
            <View style={styles.formatItem}>
              <MaterialIcons
                name="text-snippet"
                size={16}
                color="#388e3c"
                style={styles.formatIcon}
              />
              <Text style={styles.formatText}>Plain Text Files (TXT)</Text>
            </View>
          </View>
          <View style={styles.formatNoteContainer}>
            <MaterialIcons name="lightbulb" size={16} color="#ff9800" />
            <Text style={styles.formatNote}>
              Tip: For best results, use documents with clear structure and
              headings
            </Text>
          </View>
        </View>

        {/* Features Overview */}
        <View style={styles.featuresSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>What You'll Get</Text>
            <Text style={styles.sectionSubtitle}>
              Powerful AI-driven learning tools
            </Text>
          </View>

          <View style={styles.featuresGrid}>
            <View style={styles.featureCard}>
              <FontAwesome5
                name="robot"
                size={24}
                color="#007AFF"
                style={styles.featureIcon}
              />
              <Text style={styles.featureTitle}>AI-Powered Analysis</Text>
              <Text style={styles.featureText}>
                Advanced AI extracts key concepts and learning objectives
              </Text>
            </View>
            <View style={styles.featureCard}>
              <MaterialIcons
                name="quiz"
                size={24}
                color="#28a745"
                style={styles.featureIcon}
              />
              <Text style={styles.featureTitle}>Smart Flashcards</Text>
              <Text style={styles.featureText}>
                Auto-generated flashcards with difficulty levels
              </Text>
            </View>
            <View style={styles.featureCard}>
              <MaterialIcons
                name="event-note"
                size={24}
                color="#6f42c1"
                style={styles.featureIcon}
              />
              <Text style={styles.featureTitle}>Study Planning</Text>
              <Text style={styles.featureText}>
                Personalized timeline with achievable milestones
              </Text>
            </View>
            <View style={styles.featureCard}>
              <FontAwesome5
                name="brain"
                size={24}
                color="#fd7e14"
                style={styles.featureIcon}
              />
              <Text style={styles.featureTitle}>Spaced Repetition</Text>
              <Text style={styles.featureText}>
                Scientifically-proven memory retention system
              </Text>
            </View>
          </View>
        </View>

        {/* Process Details */}
        <View style={styles.processSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>How It Works</Text>
            <Text style={styles.sectionSubtitle}>
              Our AI processing pipeline transforms your document
            </Text>
          </View>

          <View style={styles.processSteps}>
            <View style={styles.processStep}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>1</Text>
              </View>
              <View style={styles.stepDetails}>
                <Text style={styles.stepTitle}>Content Analysis</Text>
                <Text style={styles.stepDescription}>
                  AI extracts and analyzes key concepts, topics, and learning
                  objectives from your document
                </Text>
              </View>
            </View>

            <View style={styles.processStep}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>2</Text>
              </View>
              <View style={styles.stepDetails}>
                <Text style={styles.stepTitle}>Study Plan Generation</Text>
                <Text style={styles.stepDescription}>
                  Creates a personalized timeline with weekly milestones and
                  achievable goals
                </Text>
              </View>
            </View>

            <View style={styles.processStep}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>3</Text>
              </View>
              <View style={styles.stepDetails}>
                <Text style={styles.stepTitle}>Smart Flashcards</Text>
                <Text style={styles.stepDescription}>
                  Generates targeted flashcards with varying difficulty levels
                  for optimal learning
                </Text>
              </View>
            </View>

            <View style={styles.processStep}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>4</Text>
              </View>
              <View style={styles.stepDetails}>
                <Text style={styles.stepTitle}>Spaced Repetition</Text>
                <Text style={styles.stepDescription}>
                  Implements scientifically-proven scheduling for maximum memory
                  retention
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Benefits Section */}
        <View style={styles.benefitsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Why Choose AI Learning?</Text>
            <Text style={styles.sectionSubtitle}>
              Join thousands of successful learners
            </Text>
          </View>

          <View style={styles.benefitsList}>
            <View style={styles.benefitItem}>
              <MaterialIcons
                name="flash-on"
                size={24}
                color="#ffc107"
                style={styles.benefitIcon}
              />
              <View style={styles.benefitContent}>
                <Text style={styles.benefitTitle}>70% Time Saved</Text>
                <Text style={styles.benefitText}>
                  Automated study planning eliminates manual work
                </Text>
              </View>
            </View>
            <View style={styles.benefitItem}>
              <MaterialIcons
                name="center-focus-strong"
                size={24}
                color="#28a745"
                style={styles.benefitIcon}
              />
              <View style={styles.benefitContent}>
                <Text style={styles.benefitTitle}>Focused Learning</Text>
                <Text style={styles.benefitText}>
                  AI identifies what matters most for your success
                </Text>
              </View>
            </View>
            <View style={styles.benefitItem}>
              <MaterialIcons
                name="trending-up"
                size={24}
                color="#007AFF"
                style={styles.benefitIcon}
              />
              <View style={styles.benefitContent}>
                <Text style={styles.benefitTitle}>50% Better Retention</Text>
                <Text style={styles.benefitText}>
                  Spaced repetition maximizes memory consolidation
                </Text>
              </View>
            </View>
            <View style={styles.benefitItem}>
              <MaterialIcons
                name="emoji-events"
                size={24}
                color="#fd7e14"
                style={styles.benefitIcon}
              />
              <View style={styles.benefitContent}>
                <Text style={styles.benefitTitle}>Faster Results</Text>
                <Text style={styles.benefitText}>
                  Achieve your learning goals in record time
                </Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f7fa",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    paddingTop: 60,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e8ecef",
  },
  backButton: {
    marginRight: 15,
    padding: 8,
  },
  backButtonText: {
    fontSize: 16,
    color: "#007AFF",
    fontWeight: "600",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "#1a1a1a",
  },
  content: {
    padding: 24,
  },

  // Hero Section
  heroSection: {
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 32,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: "#e8ecef",
  },
  titleContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 16,
  },
  titleIcon: {
    marginRight: 12,
  },
  title: {
    fontSize: 28,
    fontWeight: "800",
    color: "#1a1a1a",
  },
  subtitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#007AFF",
    marginBottom: 16,
    textAlign: "center",
  },
  description: {
    fontSize: 16,
    color: "#4a5568",
    lineHeight: 24,
    textAlign: "center",
  },

  // Section Headers
  sectionHeader: {
    marginBottom: 24,
    alignItems: "center",
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: "700",
    color: "#1a1a1a",
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: "#6b7280",
    textAlign: "center",
    lineHeight: 22,
  },

  // Upload Section
  uploadSection: {
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: "#e8ecef",
  },

  // Features Section
  featuresSection: {
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: "#e8ecef",
  },
  featuresGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  featureCard: {
    backgroundColor: "#f8fafc",
    borderRadius: 12,
    padding: 20,
    width: "48%",
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#e2e8f0",
    alignItems: "center",
  },
  featureIcon: {
    marginBottom: 12,
  },
  featureTitle: {
    fontSize: 15,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 6,
    textAlign: "center",
  },
  featureText: {
    fontSize: 13,
    color: "#6b7280",
    textAlign: "center",
    lineHeight: 18,
  },

  // Process Section
  processSection: {
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: "#e8ecef",
  },
  processSteps: {
    marginTop: 8,
  },
  processStep: {
    flexDirection: "row",
    marginBottom: 24,
    alignItems: "flex-start",
  },
  stepNumber: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "#007AFF",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 20,
    marginTop: 2,
  },
  stepNumberText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "700",
  },
  stepDetails: {
    flex: 1,
    paddingTop: 2,
  },
  stepTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 15,
    color: "#6b7280",
    lineHeight: 22,
  },
  uploadButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 24,
    paddingVertical: 18,
    borderRadius: 12,
    marginBottom: 20,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  uploadButtonSelected: {
    backgroundColor: "#10b981",
  },
  uploadButtonIcon: {
    marginRight: 10,
  },
  uploadButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  fileInfo: {
    backgroundColor: "#f8fafc",
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    borderWidth: 1,
    borderColor: "#e2e8f0",
  },
  fileDetails: {
    alignItems: "flex-start",
  },
  fileNameContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  fileName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    marginLeft: 8,
    flex: 1,
  },
  fileSize: {
    fontSize: 14,
    color: "#6b7280",
    marginBottom: 4,
  },
  fileType: {
    fontSize: 14,
    color: "#6b7280",
  },
  supportedFormats: {
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: "#e8ecef",
  },
  supportedTitleContainer: {
    alignItems: "center",
    marginBottom: 20,
  },
  supportedTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
    textAlign: "center",
  },
  formatsList: {
    marginBottom: 20,
  },
  formatItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  formatIcon: {
    marginRight: 12,
  },
  formatText: {
    fontSize: 15,
    color: "#4a5568",
    fontWeight: "500",
  },
  formatNoteContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f0f9ff",
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#bfdbfe",
  },
  formatNote: {
    fontSize: 13,
    color: "#1e40af",
    marginLeft: 8,
    flex: 1,
    lineHeight: 18,
  },
  processButton: {
    backgroundColor: "#10b981",
    padding: 18,
    borderRadius: 12,
    alignItems: "center",
    marginTop: 20,
  },
  processButtonDisabled: {
    backgroundColor: "#9ca3af",
  },
  processButtonContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  processButtonIcon: {
    marginRight: 10,
  },
  processButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  processingContainer: {
    flexDirection: "row",
    alignItems: "center",
  },

  // Benefits Section
  benefitsSection: {
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 24,
    marginBottom: 32,
    borderWidth: 1,
    borderColor: "#e8ecef",
  },
  benefitsList: {
    marginTop: 8,
  },
  benefitItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 20,
    backgroundColor: "#f8fafc",
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#e2e8f0",
  },
  benefitIcon: {
    marginRight: 16,
    marginTop: 2,
  },
  benefitContent: {
    flex: 1,
  },
  benefitTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 6,
  },
  benefitText: {
    fontSize: 14,
    color: "#6b7280",
    lineHeight: 20,
  },
});
